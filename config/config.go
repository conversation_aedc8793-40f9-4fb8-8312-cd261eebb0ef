package config

import (
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Server ServerConfig  `yaml:"server"`
	Chain  []ChainConfig `yaml:"chain"`
	Lark   LarkConfig    `yaml:"lark"`
}

type ServerConfig struct {
	Health HealthConfig `yaml:"health"`
}

type HealthConfig struct {
	Port int `yaml:"port"`
}

type LarkConfig struct {
	Webhook string `yaml:"webhook"`
}

type ChainConfig struct {
	ChainId         uint64   `yaml:"chain_id"`
	RpcUrl          string   `yaml:"rpc_url"`
	WsUrl           string   `yaml:"ws_url"`
	WNative         string   `yaml:"w_native"`
	WNativeDecimals int      `yaml:"w_native_decimals"`
	PoolAddress     string   `yaml:"pool_address"`
	NativeThreshold string   `yaml:"native_threshold"`
	WhiteList       []string `yaml:"white_list"`
}

func LoadConfig(env, confFilePath string) Config {
	configBytes, err := os.ReadFile(confFilePath)
	if err != nil {
		panic(err)
	}

	cfg := Config{}
	err = yaml.Unmarshal(configBytes, &cfg)
	if err != nil {
		panic(err)
	}

	return cfg
}
