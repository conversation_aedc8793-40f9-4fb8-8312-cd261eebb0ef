package lark

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type LarkService struct {
	webhook string
}

type LarkMessage struct {
	MsgType string  `json:"msg_type"`
	Content Content `json:"content"`
}

type Content struct {
	Text string `json:"text,omitempty"`
	Post Post   `json:"post,omitempty"`
}

type Post struct {
	ZhCn PostContent `json:"zh_cn"`
}

type PostContent struct {
	Title   string          `json:"title"`
	Content [][]PostElement `json:"content"`
}

type PostElement struct {
	Tag  string `json:"tag"`
	Text string `json:"text,omitempty"`
	Href string `json:"href,omitempty"`
}

func NewLarkService(webhook string) *LarkService {
	return &LarkService{
		webhook: webhook,
	}
}

func (l *LarkService) SendTextMessage(text string) error {
	if l.webhook == "" {
		return fmt.Errorf("webhook URL is empty")
	}

	message := LarkMessage{
		MsgType: "text",
		Content: Content{
			Text: text,
		},
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %v", err)
	}

	req, err := http.NewRequest("POST", l.webhook, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("webhook request failed with status: %d, body: %s", resp.StatusCode, string(body))
	}

	return nil
}

func (l *LarkService) SendPostMessage(title string, content [][]PostElement) error {
	if l.webhook == "" {
		return fmt.Errorf("webhook URL is empty")
	}

	message := LarkMessage{
		MsgType: "post",
		Content: Content{
			Post: Post{
				ZhCn: PostContent{
					Title:   title,
					Content: content,
				},
			},
		},
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %v", err)
	}

	req, err := http.NewRequest("POST", l.webhook, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("webhook request failed with status: %d, body: %s", resp.StatusCode, string(body))
	}

	return nil
}

func (l *LarkService) SendCombinedTradeAndPoolMessage(_ uint64, tradeType, sender, to, poolAddress, nativeToken, otherToken, nativeAmount, otherAmount, txHash string, _ uint64, reserve0, reserve1 string) error {
	// 构建富文本消息内容
	content := [][]PostElement{
		{
			{Tag: "text", Text: "🔔 检测到" + tradeType + "交易\n\n"},
		},
		{
			{Tag: "text", Text: "• 发送者: " + sender + "\n"},
		},
		{
			{Tag: "text", Text: "• 接收者: " + to + "\n"},
		},
		{
			{Tag: "text", Text: "• 池子地址: " + poolAddress + "\n"},
		},
		{
			{Tag: "text", Text: "• 卖出 Token 数量: " + otherAmount + "\n"},
		},
		{
			{Tag: "text", Text: "• 获得 JU 数量: " + nativeAmount + "\n"},
		},
		{
			{Tag: "text", Text: "• 交易链接: "},
			{Tag: "a", Text: "交易链接", Href: "https://explorer.juscan.io/tx/" + txHash},
			{Tag: "text", Text: "\n\n"},
		},
		{
			{Tag: "text", Text: "🏦 当前池子状态:\n"},
		},
		{
			{Tag: "text", Text: "• JU 储备量: " + reserve0 + "\n"},
		},
		{
			{Tag: "text", Text: "• Token 储备量: " + reserve1 + "\n\n"},
		},
		{
			{Tag: "text", Text: "⏰ " + time.Now().Format("2006-01-02 15:04:05")},
		},
	}

	return l.SendPostMessage("交易通知", content)
}

// SendTransferNotification 发送Token转账通知
func (l *LarkService) SendTransferNotification(chainId uint64, tokenAddress, from, to, amount, txHash string, blockNumber uint64) error {
	// 构建富文本消息内容
	content := [][]PostElement{
		{
			{Tag: "text", Text: "🔔 检测到非白名单地址间Token转账\n\n"},
		},
		{
			{Tag: "text", Text: "• 链ID: " + fmt.Sprintf("%d", chainId) + "\n"},
		},
		{
			{Tag: "text", Text: "• Token地址: " + tokenAddress + "\n"},
		},
		{
			{Tag: "text", Text: "• 发送方: " + from + "\n"},
		},
		{
			{Tag: "text", Text: "• 接收方: " + to + "\n"},
		},
		{
			{Tag: "text", Text: "• 交易发起者: " + sender + "\n"},
		},
		{
			{Tag: "text", Text: "• 转账数量: " + amount + "\n"},
		},
		{
			{Tag: "text", Text: "• 区块高度: " + fmt.Sprintf("%d", blockNumber) + "\n"},
		},
		{
			{Tag: "text", Text: "• 交易链接: "},
			{Tag: "a", Text: "查看交易", Href: "https://explorer.juscan.io/tx/" + txHash},
			{Tag: "text", Text: "\n\n"},
		},
		{
			{Tag: "text", Text: "⏰ " + time.Now().Format("2006-01-02 15:04:05")},
		},
	}

	return l.SendPostMessage("Token转账通知", content)
}
