package watcher

import (
	"context"
	"jc-pool-watcher/config"
	"jc-pool-watcher/consts"
	"jc-pool-watcher/contracts/erc20"
	"jc-pool-watcher/contracts/uniswapv2"
	"jc-pool-watcher/lark"
	"jc-pool-watcher/log"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"go.uber.org/zap"
)

type Watcher struct {
	chains      []config.ChainConfig
	larkService *lark.LarkService
	// 缓存每个链的 token 地址信息
	tokenCache map[uint64]*TokenInfo
}

// TokenInfo 存储池子的 token 信息
type TokenInfo struct {
	Token0      common.Address
	Token1      common.Address
	NativeToken common.Address
	OtherToken  common.Address
}

func NewWatcher(chains []config.ChainConfig, larkWebhook string) *Watcher {
	return &Watcher{
		chains:      chains,
		larkService: lark.NewLarkService(larkWebhook),
		tokenCache:  make(map[uint64]*TokenInfo),
	}
}

func (w *Watcher) Start() {
	for _, chain := range w.chains {
		go w.watchChain(chain)
	}
}

func (w *Watcher) watchChain(chain config.ChainConfig) {
	log.Logger.Info("开始监听链", zap.Uint64("chain_id", chain.ChainId), zap.String("rpc_url", chain.RpcUrl))

	client, err := ethclient.Dial(chain.RpcUrl)
	if err != nil {
		log.Logger.Error("连接RPC失败", zap.Error(err), zap.Uint64("chain_id", chain.ChainId))
		return
	}
	defer client.Close()

	// 检查是否配置了池子地址
	if chain.PoolAddress == "" {
		log.Logger.Error("未配置池子地址", zap.Uint64("chain_id", chain.ChainId))
		return
	}

	poolAddress := common.HexToAddress(chain.PoolAddress)
	log.Logger.Info("监听指定池子", zap.Uint64("chain_id", chain.ChainId), zap.String("pool_address", poolAddress.Hex()))

	// 在启动时查询并缓存 token 地址信息
	tokenInfo, err := w.initializeTokenInfo(client, poolAddress, chain)
	if err != nil {
		log.Logger.Error("初始化 token 信息失败", zap.Error(err), zap.Uint64("chain_id", chain.ChainId))
		return
	}
	w.tokenCache[chain.ChainId] = tokenInfo

	log.Logger.Info("Token 信息初始化完成",
		zap.Uint64("chain_id", chain.ChainId),
		zap.String("token0", tokenInfo.Token0.Hex()),
		zap.String("token1", tokenInfo.Token1.Hex()),
		zap.String("native_token", tokenInfo.NativeToken.Hex()),
		zap.String("other_token", tokenInfo.OtherToken.Hex()))

	// 创建 UniswapV2 Pair 过滤器
	pairFilterer, err := uniswapv2.NewPairFilterer(common.Address{}, client)
	if err != nil {
		log.Logger.Error("创建 Pair 过滤器失败", zap.Error(err))
		return
	}

	sellTopic := common.HexToHash(consts.UniswapV2SwapTopic)
	transferTopic := common.HexToHash(consts.TransferTopic)
	var lastBlock uint64

	// 获取当前区块高度
	head, err := client.HeaderByNumber(context.Background(), nil)
	if err != nil {
		log.Logger.Error("获取区块高度失败", zap.Error(err))
		return
	}
	lastBlock = head.Number.Uint64()

	for {
		time.Sleep(500 * time.Millisecond)

		head, err := client.HeaderByNumber(context.Background(), nil)
		if err != nil {
			log.Logger.Error("获取区块高度失败", zap.Error(err))
			continue
		}
		currentBlock := head.Number.Uint64()
		if currentBlock <= lastBlock {
			continue
		}

		// 监听Swap事件
		swapQuery := ethereum.FilterQuery{
			FromBlock: big.NewInt(int64(lastBlock + 1)),
			ToBlock:   big.NewInt(int64(currentBlock)),
			Addresses: []common.Address{poolAddress},
			Topics:    [][]common.Hash{{sellTopic}},
		}
		swapLogs, err := client.FilterLogs(context.Background(), swapQuery)
		if err != nil {
			log.Logger.Error("查询Swap日志失败", zap.Error(err))
			continue
		}
		for _, vLog := range swapLogs {
			w.handleSwapTransaction(client, pairFilterer, vLog, chain)
		}
	}
}

func (w *Watcher) handleSwapTransaction(client *ethclient.Client, pairFilterer *uniswapv2.PairFilterer, vLog types.Log, chain config.ChainConfig) {
	// 使用 UniswapV2 合约解析 Swap 事件
	swapEvent, err := pairFilterer.ParseSwap(vLog)
	if err != nil {
		log.Logger.Error("解析 Swap 事件失败", zap.Error(err), zap.String("tx_hash", vLog.TxHash.Hex()))
		return
	}

	poolAddress := vLog.Address

	// 获取交易的 from 地址
	tx, _, err := client.TransactionByHash(context.Background(), vLog.TxHash)
	if err != nil {
		log.Logger.Error("获取交易失败", zap.Error(err), zap.String("tx_hash", vLog.TxHash.Hex()))
		return
	}

	from, err := client.TransactionSender(context.Background(), tx, vLog.BlockHash, vLog.TxIndex)
	if err != nil {
		log.Logger.Error("获取交易发送者失败", zap.Error(err), zap.String("tx_hash", vLog.TxHash.Hex()))
		return
	}

	// 从缓存中获取 token 信息
	tokenInfo, exists := w.tokenCache[chain.ChainId]
	if !exists {
		log.Logger.Error("未找到缓存的 token 信息", zap.Uint64("chain_id", chain.ChainId))
		return
	}

	// 判断买卖方向
	var isSell bool
	var nativeOut, otherIn *big.Int

	if tokenInfo.NativeToken == tokenInfo.Token1 {
		// native 是 token1
		nativeOut = swapEvent.Amount1Out
		otherIn = swapEvent.Amount0In
	} else {
		// native 是 token0
		nativeOut = swapEvent.Amount0Out
		otherIn = swapEvent.Amount1In
	}

	// 卖出：用其他 token 兑换 native（otherIn > 0 且 nativeOut > 0）
	isSell = otherIn.Cmp(big.NewInt(0)) > 0 && nativeOut.Cmp(big.NewInt(0)) > 0

	reserves, err := w.getPoolReserves(client, poolAddress)
	if err != nil {
		log.Logger.Error("获取池子储备量失败", zap.Error(err))
		return
	}

	// 检查是否需要发送飞书消息（只有当 native token 储备量少于阈值时）
	shouldSendLark := w.shouldSendLarkMessageWithReserves(reserves, tokenInfo.NativeToken, tokenInfo.Token1, chain)

	// 格式化储备量用于消息推送
	reserve0 := formatEtherOptimized(reserves.Reserve0)
	reserve1 := formatEtherOptimized(reserves.Reserve1)

	if isSell {
		log.Logger.Info("检测到卖出交易",
			zap.Uint64("chain_id", chain.ChainId),
			zap.String("from", from.Hex()),
			zap.String("to", swapEvent.To.Hex()),
			zap.String("pool_address", poolAddress.Hex()),
			zap.String("native_token", tokenInfo.NativeToken.Hex()),
			zap.String("other_token", tokenInfo.OtherToken.Hex()),
			zap.String("other_in", otherIn.String()),
			zap.String("native_out", nativeOut.String()),
			zap.String("other_in_eth", formatEther(otherIn)),
			zap.String("native_out_eth", formatEther(nativeOut)),
			zap.String("tx_hash", vLog.TxHash.Hex()),
			zap.Uint64("block_number", vLog.BlockNumber),
		)

		// 只有在需要时才发送飞书消息
		if shouldSendLark {
			err = w.larkService.SendCombinedTradeAndPoolMessage(
				chain.ChainId,
				"卖出",
				from.Hex(),
				swapEvent.To.Hex(),
				poolAddress.Hex(),
				tokenInfo.NativeToken.Hex(),
				tokenInfo.OtherToken.Hex(),
				formatEtherOptimized(otherIn),
				formatEtherOptimized(nativeOut),
				vLog.TxHash.Hex(),
				vLog.BlockNumber,
				reserve0,
				reserve1,
			)
			if err != nil {
				log.Logger.Error("发送飞书消息失败", zap.Error(err))
			}
		}
	}
}

func (w *Watcher) handleTransferTransaction(client *ethclient.Client, erc20Filterer *erc20.Erc20Filterer, vLog types.Log, chain config.ChainConfig) {
	// 解析Transfer事件
	transferEvent, err := erc20Filterer.ParseTransfer(vLog)
	if err != nil {
		log.Logger.Error("解析Transfer事件失败", zap.Error(err), zap.String("tx_hash", vLog.TxHash.Hex()))
		return
	}

	// 检查是否为白名单地址间的转账
	if w.isWhitelistTransfer(transferEvent.From, transferEvent.To, chain.WhiteList) {
		log.Logger.Debug("白名单地址间转账，跳过通知",
			zap.String("from", transferEvent.From.Hex()),
			zap.String("to", transferEvent.To.Hex()),
			zap.String("tx_hash", vLog.TxHash.Hex()))
		return
	}

	// 获取交易详情
	tx, _, err := client.TransactionByHash(context.Background(), vLog.TxHash)
	if err != nil {
		log.Logger.Error("获取交易失败", zap.Error(err), zap.String("tx_hash", vLog.TxHash.Hex()))
		return
	}

	// 获取交易发送者
	sender, err := client.TransactionSender(context.Background(), tx, vLog.BlockHash, vLog.TxIndex)
	if err != nil {
		log.Logger.Error("获取交易发送者失败", zap.Error(err), zap.String("tx_hash", vLog.TxHash.Hex()))
		return
	}

	log.Logger.Info("检测到非白名单地址间转账",
		zap.Uint64("chain_id", chain.ChainId),
		zap.String("token_address", vLog.Address.Hex()),
		zap.String("from", transferEvent.From.Hex()),
		zap.String("to", transferEvent.To.Hex()),
		zap.String("sender", sender.Hex()),
		zap.String("value", transferEvent.Value.String()),
		zap.String("value_eth", formatEther(transferEvent.Value)),
		zap.String("tx_hash", vLog.TxHash.Hex()),
		zap.Uint64("block_number", vLog.BlockNumber),
	)

	// 发送Lark通知
	err = w.larkService.SendTransferNotification(
		chain.ChainId,
		vLog.Address.Hex(),
		transferEvent.From.Hex(),
		transferEvent.To.Hex(),
		sender.Hex(),
		formatEtherOptimized(transferEvent.Value),
		vLog.TxHash.Hex(),
		vLog.BlockNumber,
	)
	if err != nil {
		log.Logger.Error("发送Transfer通知失败", zap.Error(err))
	}
}

func (w *Watcher) getTokenAddresses(client *ethclient.Client, poolAddress common.Address) (common.Address, common.Address, error) {
	// 创建 Pair 合约实例
	pair, err := uniswapv2.NewPair(poolAddress, client)
	if err != nil {
		return common.Address{}, common.Address{}, err
	}

	// 获取 token0 和 token1 地址
	token0, err := pair.Token0(nil)
	if err != nil {
		return common.Address{}, common.Address{}, err
	}

	token1, err := pair.Token1(nil)
	if err != nil {
		return common.Address{}, common.Address{}, err
	}

	return token0, token1, nil
}

func (w *Watcher) getPoolReserves(client *ethclient.Client, poolAddress common.Address) (*struct {
	Reserve0           *big.Int
	Reserve1           *big.Int
	BlockTimestampLast uint32
}, error) {
	// 创建 Pair 合约实例
	pair, err := uniswapv2.NewPair(poolAddress, client)
	if err != nil {
		return nil, err
	}

	// 获取池子储备量
	reserves, err := pair.GetReserves(nil)
	if err != nil {
		return nil, err
	}

	return &reserves, nil
}

func (w *Watcher) shouldSendLarkMessageWithReserves(reserves *struct {
	Reserve0           *big.Int
	Reserve1           *big.Int
	BlockTimestampLast uint32
}, nativeToken, token1 common.Address, chain config.ChainConfig) bool {
	// 如果没有配置阈值，默认发送消息
	if chain.NativeThreshold == "" {
		log.Logger.Info("未配置阈值，默认发送消息")
		return true
	}

	// 解析配置的阈值
	threshold, ok := new(big.Int).SetString(chain.NativeThreshold, 10)
	if !ok {
		log.Logger.Error("解析 native threshold 失败", zap.String("threshold", chain.NativeThreshold))
		return true
	}

	// 获取 native token 的储备量
	var nativeReserve *big.Int
	if nativeToken == token1 {
		nativeReserve = reserves.Reserve1
	} else {
		nativeReserve = reserves.Reserve0
	}

	// 只有当 native token 储备量少于阈值时才发送消息
	return nativeReserve.Cmp(threshold) < 0
}

func formatEther(wei *big.Int) string {
	ether := new(big.Float).Quo(new(big.Float).SetInt(wei), new(big.Float).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(18), nil)))
	return ether.Text('f', 18)
}

// formatEtherOptimized 优化数量格式，去除末尾的零
func formatEtherOptimized(wei *big.Int) string {
	ether := new(big.Float).Quo(new(big.Float).SetInt(wei), new(big.Float).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(18), nil)))
	formatted := ether.Text('f', 18)

	// 去除末尾的零
	for len(formatted) > 0 && formatted[len(formatted)-1] == '0' {
		formatted = formatted[:len(formatted)-1]
	}

	// 如果最后一位是小数点，也去掉
	if len(formatted) > 0 && formatted[len(formatted)-1] == '.' {
		formatted = formatted[:len(formatted)-1]
	}

	return formatted
}

func (w *Watcher) initializeTokenInfo(client *ethclient.Client, poolAddress common.Address, chain config.ChainConfig) (*TokenInfo, error) {
	// 获取 token0 和 token1 地址
	token0, token1, err := w.getTokenAddresses(client, poolAddress)
	if err != nil {
		return nil, err
	}

	// 判断哪个是 native 货币（计价货币）
	var nativeToken, otherToken common.Address
	configNativeToken := common.HexToAddress(chain.WNative)

	if token1 == configNativeToken {
		nativeToken = token1
		otherToken = token0
	} else if token0 == configNativeToken {
		nativeToken = token0
		otherToken = token1
	} else {
		// 如果没有找到配置的 native token，假设 token1 是 native 货币
		nativeToken = token1
		otherToken = token0
		log.Logger.Warn("未找到配置的 native token，使用默认 token1",
			zap.String("config_native", configNativeToken.Hex()),
			zap.String("token0", token0.Hex()),
			zap.String("token1", token1.Hex()))
	}

	// 创建 TokenInfo 对象
	tokenInfo := &TokenInfo{
		Token0:      token0,
		Token1:      token1,
		NativeToken: nativeToken,
		OtherToken:  otherToken,
	}

	return tokenInfo, nil
}

// isWhitelistTransfer 检查转账是否发生在白名单地址之间
func (w *Watcher) isWhitelistTransfer(from, to common.Address, whitelist []string) bool {
	if len(whitelist) == 0 {
		return false
	}

	fromInWhitelist := false
	toInWhitelist := false

	// 检查发送方是否在白名单中
	for _, addr := range whitelist {
		if strings.EqualFold(from.Hex(), addr) {
			fromInWhitelist = true
			break
		}
	}

	// 检查接收方是否在白名单中
	for _, addr := range whitelist {
		if strings.EqualFold(to.Hex(), addr) {
			toInWhitelist = true
			break
		}
	}

	// 只有当发送方和接收方都在白名单中时，才认为是白名单间转账
	return fromInWhitelist && toInWhitelist
}
